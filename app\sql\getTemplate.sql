-- Optimized query to get template with all related data in single query
-- This eliminates N+1 problem by using JOINs instead of multiple separate queries
SELECT
    t.id as template_id,
    t.tem_name,
    t.tem_desc,
    t.status as template_status,
    t.price as template_price,

    g.id as group_id,
    g.gr_name as group_name,
    g.tooltips as group_tooltips,
    g.answer_id as group_answer_id,
    g.created_at as group_created_at,

    q.id as question_id,
    q.name as question_name,
    q.text as question_text,
    q.answer_type,
    q.required,
    q.tooltips as question_tooltips,
    q.selectAnswerTable,
    q.parent_answer,
    q.created_at as question_created_at,

    a.ans_id,
    a.name as answer_name,
    a.modal_id,
    a.modal_type,
    a.answer as answer_content,

    ans.id as answers_id,
    ans.name as answers_name,
    ans.text as answers_text

FROM templates t
LEFT JOIN `groups` g ON g.tem_id = t.id
LEFT JOIN questions q ON q.gr_id = g.id
LEFT JOIN answer a ON a.ques_id = q.id
LEFT JOIN answers ans ON ans.ques_id = a.ans_id
WHERE t.id = ?
ORDER BY g.gr_id, q.ques_id, a.answer_id;