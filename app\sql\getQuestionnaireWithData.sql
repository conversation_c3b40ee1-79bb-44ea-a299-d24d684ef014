-- Optimized query to get questionnaire with all related data in single query
-- This eliminates N+1 problem by using JOINs instead of multiple separate queries
SELECT 
    q.id as questionnaire_id,
    q.qtn_name,
    q.qtn_desc,
    q.status as questionnaire_status,
    q.tem_id,
    q.lf_id,
    q.price as questionnaire_price,
    
    -- Template and law firm info
    t.tem_name,
    l.lf_org_name,
    
    g.id as group_id,
    g.gr_name as group_name,
    g.tooltips as group_tooltips,
    g.answer_id as group_answer_id,
    g.created_at as group_created_at,

    ques.id as question_id,
    ques.name as question_name,
    ques.text as question_text,
    ques.answer_type,
    ques.required,
    ques.tooltips as question_tooltips,
    ques.selectAnswerTable,
    ques.parent_answer,
    ques.path as question_files,
    ques.created_at as question_created_at,
    
    a.ans_id,
    a.name as answer_name,
    a.modal_id,
    a.modal_type,
    a.answer as answer_content,
    
    ans.id as answers_id,
    ans.name as answers_name,
    ans.text as answers_text
    
FROM questionaires q
JOIN law_firm l ON l.lf_id = q.lf_id
JOIN templates t ON t.id = q.tem_id
LEFT JOIN `groups` g ON g.tem_id = q.id
LEFT JOIN questions ques ON ques.gr_id = g.id
LEFT JOIN answer a ON a.ques_id = ques.id
LEFT JOIN answers ans ON ans.ques_id = a.ans_id
WHERE q.id = ?
ORDER BY g.id, ques.id, a.id, ans.answer_id;
